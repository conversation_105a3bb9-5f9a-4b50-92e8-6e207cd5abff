<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Allow CORS (for development, make sure this is limited to specific origins in production)
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

if($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    if (isset($_SERVER["HTTP_ACCESS_CONTROL_REQUEST_METHOD"])) {
        header("Access-Control-Allow-Methods: POST, GET, OPTIONS, DELETE, PUT"); 
    }
    if (isset($_SERVER["HTTP_ACCESS_CONTROL_REQUEST_HEADERS"])) {
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    }
    exit(0);
}

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

// Include PHPMailer library files
require 'PHPMailer/Exception.php';
require 'PHPMailer/PHPMailer.php';
require 'PHPMailer/SMTP.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize variables
    $company_name = $_POST['company'] ?? '';
    $first_name = $_POST['firstName'] ?? '';
    $last_name = $_POST['lastName'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone_number = $_POST['phone'] ?? '';
    $message = $_POST['message'] ?? '';
    $languageCode = $_POST['languageCode'] ?? '';

    function sendEmailToAdmin($to_admin, $subject_admin, $body_admin, $smtp_host_admin, $smtp_username_admin, $smtp_password_admin, $from_email_admin = '', $from_name_admin = '') {
        $mail_admin = new PHPMailer(true); // Enable exceptions for error handling
        try {
            //Server settings
            $mail_admin->isSMTP(); // Set mailer to use SMTP
            $mail_admin->Host = $smtp_host_admin; // Specify main and backup SMTP servers
            $mail_admin->SMTPAuth = true; // Enable SMTP authentication
            $mail_admin->Username = $smtp_username_admin; // SMTP username
            $mail_admin->Password = $smtp_password_admin; // SMTP password
            $mail_admin->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
            $mail_admin->Port = 465; // TCP port to connect to

            // Recipients
            $mail_admin->setFrom($from_email_admin, $from_name_admin); // Sender's email and name
            $mail_admin->addAddress($to_admin); // Add a recipient

            // Ensure email is in UTF-8
            $mail_admin->CharSet = 'UTF-8'; // Set the email charset to UTF-8

            // Content
            $mail_admin->isHTML(false); // Set email format to plain text
            $mail_admin->Subject = mb_encode_mimeheader($subject_admin, 'UTF-8'); // Email subject
            $body_admin = mb_convert_encoding($body_admin, 'UTF-8', 'auto'); // Ensure body is UTF-8 encoded
            $mail_admin->Body = $body_admin; // Email body content

            // Send the email
            $mail_admin->send();
            return true;
        } catch (Exception $e) {
            return false; // Display error message
        }
    }

    // SMTP details
    $smtp_host_admin = 'w01f37ba.kasserver.com'; // Your SMTP host
    $smtp_username_admin = 'm0728998'; // Your SMTP username
    $smtp_password_admin = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
    $from_email_admin = '<EMAIL>'; // Sender's email
    $from_name_admin = 'GH-Appartements'; // Sender's name

    // Email details
    $to_admin = '<EMAIL>';
    $subject_admin = 'Eine neue Anfrage auf www.gh-appartements.de';
    
    // Encode the subject to handle special characters
    $encoded_subject_admin = mb_encode_mimeheader($subject_admin, 'UTF-8');
    $body_admin = 'Eine neue Anfrage wurde soeben abgesendet. Hier sind die Einzelheiten:' . "\n\n" .
                  'Name: ' . $first_name . ' ' . $last_name . "\n" .
                  'Unternehmen: ' . $company_name . "\n" .
                  'E-Mail: ' . $email . ' Tel.: ' . $phone_number . "\n" .
                  'Nachricht: ' . $message . "\n\n";

    // Send email
    $result_admin = sendEmailToAdmin($to_admin, $encoded_subject_admin, $body_admin, $smtp_host_admin, $smtp_username_admin, $smtp_password_admin, $from_email_admin, $from_name_admin);

    echo $result_admin; // Output the result of email sending

    if ($languageCode == 'de') {
        function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email = '', $from_name = '') {
            $mail = new PHPMailer(true); // Enable exceptions for error handling
            try {
                //Server settings
                $mail->isSMTP(); // Set mailer to use SMTP
                $mail->Host = $smtp_host; // Specify main and backup SMTP servers
                $mail->SMTPAuth = true; // Enable SMTP authentication
                $mail->Username = $smtp_username; // SMTP username
                $mail->Password = $smtp_password; // SMTP password
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
                $mail->Port = 465; // TCP port to connect to
    
                // Recipients
                $mail->setFrom($from_email, $from_name); // Sender's email and name
                $mail->addAddress($to); // Add a recipient
    
                // Content
                $mail->isHTML(false); // Set email format to plain text
                $mail->Subject = mb_encode_mimeheader($subject, 'UTF-8'); // Email subject
                $mail->Body = $body; // Email body content
    
                // Send the email
                $mail->send();
                return true;
            } catch (Exception $e) {
                return false; // Display error message
            }
        }
        // SMTP details
        $smtp_host = 'w01f37ba.kasserver.com'; // Your SMTP host
        $smtp_username = 'm0728998'; // Your SMTP username
        $smtp_password = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
        $from_email = '<EMAIL>'; // Sender's email
        $from_name = 'GH-Appartements'; // Sender's name
    
        // Email details
        $to = $email;
        $subject = 'Vielen dank für Ihre Anfrage auf www.gh-appartements.de';
        
        // Encode the subject to handle special characters
        $encoded_subject = mb_encode_mimeheader($subject, 'UTF-8');
        $body = 'Hallo ' . $first_name . ',' . "\n\n" .
                'Wir bedanken uns für Ihr Interesse und arbeiten mit Hochdruck an der Bearbeitung Ihrer Anfrage und' . "\n" .
                'werden uns schnellstmöglich bei Ihnen melden.' . "\n\n" .
                'Mit besten Grüßen' . "\n" .
                'Ihr GH-Team';
    
        // Send email
        $result = sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email, $from_name);
    
        echo $result; // Output the result of email sending

    }
   
    elseif ($languageCode == 'en') {
        function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email = '', $from_name = '') {
            $mail = new PHPMailer(true); // Enable exceptions for error handling
            try {
                //Server settings
                $mail->isSMTP(); // Set mailer to use SMTP
                $mail->Host = $smtp_host; // Specify main and backup SMTP servers
                $mail->SMTPAuth = true; // Enable SMTP authentication
                $mail->Username = $smtp_username; // SMTP username
                $mail->Password = $smtp_password; // SMTP password
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
                $mail->Port = 465; // TCP port to connect to
    
                // Recipients
                $mail->setFrom($from_email, $from_name); // Sender's email and name
                $mail->addAddress($to); // Add a recipient
    
                // Content
                $mail->isHTML(false); // Set email format to plain text
                $mail->Subject = mb_encode_mimeheader($subject, 'UTF-8'); // Email subject
                $mail->Body = $body; // Email body content
    
                // Send the email
                $mail->send();
                return true;
            } catch (Exception $e) {
                return false; // Display error message
            }
        }
    
        // SMTP details
        $smtp_host = 'w01f37ba.kasserver.com'; // Your SMTP host
        $smtp_username = 'm0728998'; // Your SMTP username
        $smtp_password = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
        $from_email = '<EMAIL>'; // Sender's email
        $from_name = 'GH-Appartements'; // Sender's name
    
        // Email details
        $to = $email;
        $subject = 'Thank you for your inquiry on www.gh-appartements.de';
        
        // Encode the subject to handle special characters
        $encoded_subject = mb_encode_mimeheader($subject, 'UTF-8');
        $body = 'Hello ' . $first_name . ',' . "\n\n" .
                'We thank you for your interest and are working hard to process your request and' . "\n" .
                'we will contact you as soon as possible.' . "\n\n" .
                'Best regards' . "\n" .
                'Your GH-Team';
    
        // Send email
        $result = sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email, $from_name);
    
        echo $result; // Output the result of email sending
    }

    elseif ($languageCode == 'fr') {
        function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email = '', $from_name = '') {
            $mail = new PHPMailer(true); // Enable exceptions for error handling
            try {
                //Server settings
                $mail->isSMTP(); // Set mailer to use SMTP
                $mail->Host = $smtp_host; // Specify main and backup SMTP servers
                $mail->SMTPAuth = true; // Enable SMTP authentication
                $mail->Username = $smtp_username; // SMTP username
                $mail->Password = $smtp_password; // SMTP password
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
                $mail->Port = 465; // TCP port to connect to
    
                // Recipients
                $mail->setFrom($from_email, $from_name); // Sender's email and name
                $mail->addAddress($to); // Add a recipient
    
                // Content
                $mail->isHTML(false); // Set email format to plain text
                $mail->Subject = mb_encode_mimeheader($subject, 'UTF-8'); // Email subject
                $mail->Body = $body; // Email body content
    
                // Send the email
                $mail->send();
                return true;
            } catch (Exception $e) {
                return false; // Display error message
            }
        }
    
        // SMTP details
        $smtp_host = 'w01f37ba.kasserver.com'; // Your SMTP host
        $smtp_username = 'm0728998'; // Your SMTP username
        $smtp_password = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
        $from_email = '<EMAIL>'; // Sender's email
        $from_name = 'GH-Appartements'; // Sender's name
    
        // Email details
        $to = $email;
        $subject = 'Merci pour votre demande sur www.gh-appartements.de';
        
        // Encode the subject to handle special characters
        $encoded_subject = mb_encode_mimeheader($subject, 'UTF-8');
        $body = 'Bonjour ' . $first_name . ',' . "\n\n" .
                'Nous vous remercions de votre intérêt et travaillons dur pour traiter votre demande et' . "\n" .
                'vous répondrons dans les plus brefs délais.' . "\n\n" .
                'Cordialement' . "\n" .
                'Votre GH-Team';
    
        // Send email
        $result = sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email, $from_name);
    
        echo $result; // Output the result of email sending
    }

    elseif ($languageCode == 'it') {
        function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email = '', $from_name = '') {
            $mail = new PHPMailer(true); // Enable exceptions for error handling
            try {
                //Server settings
                $mail->isSMTP(); // Set mailer to use SMTP
                $mail->Host = $smtp_host; // Specify main and backup SMTP servers
                $mail->SMTPAuth = true; // Enable SMTP authentication
                $mail->Username = $smtp_username; // SMTP username
                $mail->Password = $smtp_password; // SMTP password
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
                $mail->Port = 465; // TCP port to connect to
    
                // Recipients
                $mail->setFrom($from_email, $from_name); // Sender's email and name
                $mail->addAddress($to); // Add a recipient
    
                // Content
                $mail->isHTML(false); // Set email format to plain text
                $mail->Subject = mb_encode_mimeheader($subject, 'UTF-8'); // Email subject
                $mail->Body = $body; // Email body content
    
                // Send the email
                $mail->send();
                return true;
            } catch (Exception $e) {
                return false; // Display error message
            }
        }
    
        // SMTP details
        $smtp_host = 'w01f37ba.kasserver.com'; // Your SMTP host
        $smtp_username = 'm0728998'; // Your SMTP username
        $smtp_password = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
        $from_email = '<EMAIL>'; // Sender's email
        $from_name = 'GH-Appartements'; // Sender's name
    
        // Email details
        $to = $email;
        $subject = 'Grazie per la tua richiesta su www.gh-appartements.de';
        
        // Encode the subject to handle special characters
        $encoded_subject = mb_encode_mimeheader($subject, 'UTF-8');
        $body = 'Ciao ' . $first_name . ',' . "\n\n" .
                'Ti ringraziamo per il tuo interesse e stiamo lavorando duramente per elaborare' . "\n" .
                'la tua richiesta e ti ricontatteremo il prima possibile.' . "\n\n" .
                'Distinti saluti' . "\n" .
                'La tua GH-Team';
    
        // Send email
        $result = sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email, $from_name);
    
        echo $result; // Output the result of email sending    
    }

    elseif ($languageCode == 'pl') {
        function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email = '', $from_name = '') {
            $mail = new PHPMailer(true); // Enable exceptions for error handling
            try {
                //Server settings
                $mail->isSMTP(); // Set mailer to use SMTP
                $mail->Host = $smtp_host; // Specify main and backup SMTP servers
                $mail->SMTPAuth = true; // Enable SMTP authentication
                $mail->Username = $smtp_username; // SMTP username
                $mail->Password = $smtp_password; // SMTP password
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
                $mail->Port = 465; // TCP port to connect to
    
                // Recipients
                $mail->setFrom($from_email, $from_name); // Sender's email and name
                $mail->addAddress($to); // Add a recipient
    
                // Content
                $mail->isHTML(false); // Set email format to plain text
                $mail->Subject = mb_encode_mimeheader($subject, 'UTF-8'); // Email subject
                $mail->Body = $body; // Email body content
    
                // Send the email
                $mail->send();
                return true;
            } catch (Exception $e) {
                return false; // Display error message
            }
        }
    
        // SMTP details
        $smtp_host = 'w01f37ba.kasserver.com'; // Your SMTP host
        $smtp_username = 'm0728998'; // Your SMTP username
        $smtp_password = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
        $from_email = '<EMAIL>'; // Sender's email
        $from_name = 'GH-Appartements'; // Sender's name
    
        // Email details
        $to = $email;
        $subject = 'Dziękujemy za zapytanie na stronie www.gh-appartements.de';
        
        // Encode the subject to handle special characters
        $encoded_subject = mb_encode_mimeheader($subject, 'UTF-8');
        $body = 'Cześć ' . $first_name . ',' . "\n\n" .
                'Dziękujemy za zainteresowanie i ciężko pracujemy, aby rozpatrzyć Twoją prośbę. ' . "\n" .
                'Skontaktujemy się z Tobą tak szybko, jak to możliwe.' . "\n\n" .
                'Z wyrazami szacunku' . "\n" .
                'Twój GH-Team';
    
        // Send email
        $result = sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email, $from_name);
    
        echo $result; // Output the result of email sending
    }

    elseif ($languageCode == 'ua') {
        function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email = '', $from_name = '') {
            $mail = new PHPMailer(true); // Enable exceptions for error handling
            try {
                //Server settings
                $mail->isSMTP(); // Set mailer to use SMTP
                $mail->Host = $smtp_host; // Specify main and backup SMTP servers
                $mail->SMTPAuth = true; // Enable SMTP authentication
                $mail->Username = $smtp_username; // SMTP username
                $mail->Password = $smtp_password; // SMTP password
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Enable SSL encryption
                $mail->Port = 465; // TCP port to connect to
    
                // Recipients
                $mail->setFrom($from_email, $from_name); // Sender's email and name
                $mail->addAddress($to); // Add a recipient
    
                // Content
                $mail->isHTML(false); // Set email format to plain text
                $mail->Subject = mb_encode_mimeheader($subject, 'UTF-8'); // Email subject
                $mail->Body = $body; // Email body content
    
                // Send the email
                $mail->send();
                return true;
            } catch (Exception $e) {
                return false; // Display error message
            }
        }
    
        // SMTP details
        $smtp_host = 'w01f37ba.kasserver.com'; // Your SMTP host
        $smtp_username = 'm0728998'; // Your SMTP username
        $smtp_password = 'aSBy8GYX8ChChuCXDiyr'; // Your SMTP password
        $from_email = '<EMAIL>'; // Sender's email
        $from_name = 'GH-Appartements'; // Sender's name
    
        // Email details
        $to = $email;
        $subject = 'Дякуємо за ваш запит на сайті www.gh-appartements.de';
        
        // Encode the subject to handle special characters
        $encoded_subject = mb_encode_mimeheader($subject, 'UTF-8');
        $body = 'Привіт ' . $first_name . ',' . "\n\n" .
                'Ми дякуємо вам за ваш інтерес і наполегливо працюємо над обробкою вашого запиту та' . "\n" .
                'зв’яжемося з вами якомога швидше.' . "\n\n" .
                'З повагою' . "\n" .
                'Ваш GH-Team';
    
        // Send email
        $result = sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $from_email, $from_name);
    
        echo $result; // Output the result of email sending
    } 
}


?>