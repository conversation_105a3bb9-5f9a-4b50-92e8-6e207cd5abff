<PERSON> Mailer
------------

**Swiftmail<PERSON> will stop being maintained at the end of November 2021.**

Please, move to [Symfony Mailer](https://symfony.com/doc/current/mailer.html) at your earliest convenience.
[Symfony Mailer](https://symfony.com/doc/current/mailer.html) is the next evolution of Swiftmailer.
It provides the same features with support for modern PHP code and support for third-party providers.

Swift Mailer is a component based mailing solution for PHP.
It is released under the MIT license.

Swift Mailer is highly object-oriented by design and lends itself
to use in complex web application with a great deal of flexibility.

For full details on usage, read the [documentation](https://swiftmailer.symfony.com/docs/introduction.html).

Sponsors
--------

<div>
    <a href="https://blackfire.io/docs/introduction?utm_source=swiftmailer&utm_medium=github_readme&utm_campaign=logo">
        <img src="https://static.blackfire.io/assets/intemporals/logo/png/blackfire-io_secondary_horizontal_transparent.png?1" width="255px" alt="Blackfire.io">
    </a>
</div>
