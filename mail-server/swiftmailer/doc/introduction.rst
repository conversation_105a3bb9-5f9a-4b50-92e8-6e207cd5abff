Swift Mailer: A feature-rich PHP Mailer
=======================================

Swift Mailer is a component based library for sending e-mails from PHP applications.

**Swift Mailer will stop being maintained at the end of November 2021.**

Please, move to `Symfony Mailer <https://symfony.com/doc/current/mailer.html>`_ at your earliest convenience.
`Symfony Mailer <https://symfony.com/doc/current/mailer.html>`_ is the next evolution of Swift Mailer.
It provides the same features with support for modern PHP code and support for third-party providers.

System Requirements
-------------------

Swift Mailer supports PHP 7.0 to PHP 8.1 included (``proc_*`` functions must be
available).

Swift Mailer does not work when used with function overloading as implemented
by ``mbstring`` when ``mbstring.func_overload`` is set to ``2``.

Installation
------------

The recommended way to install Swiftmailer is via Composer:

.. code-block:: bash

    $ composer require "swiftmailer/swiftmailer:^6.0"

Basic Usage
-----------

Here is the simplest way to send emails with Swift Mailer::

    require_once '/path/to/vendor/autoload.php';

    // Create the Transport
    $transport = (new Swift_SmtpTransport('smtp.example.org', 25))
      ->setUsername('your username')
      ->setPassword('your password')
    ;

    // Create the Mailer using your created Transport
    $mailer = new Swift_Mailer($transport);

    // Create a message
    $message = (new Swift_Message('Wonderful Subject'))
      ->setFrom(['<EMAIL>' => 'John Doe'])
      ->setTo(['<EMAIL>', '<EMAIL>' => 'A name'])
      ->setBody('Here is the message itself')
      ;

    // Send the message
    $result = $mailer->send($message);

You can also use Sendmail as a transport::

    // Sendmail
    $transport = new Swift_SendmailTransport('/usr/sbin/sendmail -bs');

Getting Help
------------

For general support, use `Stack Overflow <https://stackoverflow.com>`_.

For bug reports and feature requests, create a new ticket in `GitHub
<https://github.com/swiftmailer/swiftmailer/issues>`_.
