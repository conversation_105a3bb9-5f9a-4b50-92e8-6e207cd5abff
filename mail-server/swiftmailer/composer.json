{"name": "swiftmailer/swiftmailer", "type": "library", "description": "Swiftmailer, free feature-rich PHP mailer", "keywords": ["mail", "mailer", "email"], "homepage": "https://swiftmailer.symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.0.0", "egulias/email-validator": "^2.0|^3.1", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "autoload": {"files": ["lib/swift_required.php"]}, "autoload-dev": {"psr-0": {"Swift_": "tests/unit"}}, "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "minimum-stability": "dev", "prefer-stable": true}