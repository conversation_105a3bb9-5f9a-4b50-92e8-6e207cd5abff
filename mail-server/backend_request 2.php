<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize variables
    $company_name = $_POST['company'] ?? '';
    $first_name = $_POST['firstName'] ?? '';
    $last_name = $_POST['lastName'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone_number = $_POST['phone'] ?? '';
    $message = $_POST['message'] ?? '';
    $languageCode = $_POST['languageCode'] ?? '';

    // Email sending code
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\SMTP;
    use PHPMailer\PHPMailer\Exception;

    require 'PHPMailer/PHPMailer.php';
    require 'PHPMailer/SMTP.php';
    require 'PHPMailer/Exception.php';

    function sendEmail($to, $subject, $body, $smtp_host, $smtp_username, $smtp_password, $smtp_port, $from_email, $from_name) {
        $mail = new PHPMailer(true);

        try {
            // SMTP configuration
            $mail->isSMTP();
            $mail->Host = $smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $smtp_username;
            $mail->Password = $smtp_password;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Use SMTPS encryption (for port 465)
            $mail->Port = $smtp_port;

            // Email headers
            $mail->setFrom($from_email, $from_name);
            $mail->addAddress($to);

            // Email content
            $mail->isHTML(false); // Set email format to plain text
            $mail->Subject = $subject;
            $mail->Body = $body;

            $mail->send();
            return 'Success';
        } catch (Exception $e) {
            return "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
        }
    }

    if (!empty($email) && !empty($first_name)) {
        // Send a confirmation email to the client
        $to_client = $email;
        $subject_client = 'Vielen dank für Ihre Anfrage auf www.gh-appartements.de';
        $body_client = 'Hallo ' . $first_name . ',' . "\n\n" .
            'Wir bedanken uns für Ihr Interesse und arbeiten mit Hochdruck an der Bearbeitung Ihrer Anfrage und' . "\n" .
            'werden uns schnellstmöglich bei Ihnen melden.' . "\n\n" .
            'Mit besten Grüßen' . "\n" .
            'Ihr GH-Team';

        // New SMTP credentials
        $smtp_host = 'w01f37ba.kasserver.com';
        $smtp_username = 'm07288f1';
        $smtp_password = 'uB5UmFPWHKuaT2EuovP';
        $smtp_port = 465; // Port 465 with SMTPS encryption
        $from_email = '<EMAIL>';
        $from_name = 'GH-Appartements GmbH';

        // Send confirmation email to the client
        $result_client = sendEmail($to_client, $subject_client, $body_client, $smtp_host, $smtp_username, $smtp_password, $smtp_port, $from_email, $from_name);

        // Send a notification email to the admin
        $to_admin = '<EMAIL>'; // Change to actual admin email
        $subject_admin = 'New Request Received';
        $body_admin = 'New request received from ' . $first_name . ' ' . $last_name . '.' . "\n\n" .
            'Details:' . "\n" .
            'Company: ' . $company_name . "\n" .
            'Email: ' . $email . "\n" .
            'Phone: ' . $phone_number . "\n" .
            'Message: ' . $message . "\n" .
            'Language: ' . $languageCode;

        // Send notification email to the admin
        $result_admin = sendEmail($to_admin, $subject_admin, $body_admin, $smtp_host, $smtp_username, $smtp_password, $smtp_port, $from_email, $from_name);

        // Log the results
        file_put_contents('email_log.txt', "Client Email Result: $result_client\nAdmin Email Result: $result_admin\n", FILE_APPEND);

        // Send a JSON response back to the Angular app
        echo json_encode(['success' => true, 'message' => 'Request submitted successfully.']);
    } else {
        // Missing required fields
        echo json_encode(['success' => false, 'message' => 'Missing required fields.']);
    }
} else {
    // Not a POST request
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>
